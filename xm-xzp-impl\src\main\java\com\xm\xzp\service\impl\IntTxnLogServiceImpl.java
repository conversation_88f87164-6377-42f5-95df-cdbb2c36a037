package com.xm.xzp.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xm.xzp.mapper.IntTxnLogMapper;
import com.xm.xzp.model.entity.IntTxnLog;
import com.xm.xzp.model.vo.IntTxnLogResultVo;
import com.xm.xzp.model.vo.IntTxnLogVo;
import com.xm.xzp.service.IIntTxnLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class IntTxnLogServiceImpl extends ServiceImpl<IntTxnLogMapper, IntTxnLog> 
        implements IIntTxnLogService {

    @Resource
    private IntTxnLogMapper intTxnLogMapper;

    @Override
    @DS("datasource2")
    public PageInfo<IntTxnLogResultVo> selectIntTxnLogList(IntTxnLogVo intTxnLog, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<IntTxnLogResultVo> list = intTxnLogMapper.selectIntTxnLogList(intTxnLog);
        return new PageInfo<>(list);
    }

    @Override
    @DS("datasource2")
    public PageInfo<IntTxnLog> intTxnLogList(IntTxnLogVo intTxnLog, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<IntTxnLog> list = this.list(getIntTxnLogQueryWrapper(intTxnLog));
        return new PageInfo<>(list);
    }

    private QueryWrapper<IntTxnLog> getIntTxnLogQueryWrapper(IntTxnLogVo intTxnLog) {
        QueryWrapper<IntTxnLog> queryWrapper = new QueryWrapper<>();
        if (intTxnLog != null) {
            LambdaQueryWrapper<IntTxnLog> lambdaQueryWrapper = queryWrapper.lambda();
            if (StringUtils.isNotBlank(intTxnLog.getStartTime()) && 
                StringUtils.isNotBlank(intTxnLog.getEndTime())) {
                lambdaQueryWrapper.le(IntTxnLog::getTranDt, intTxnLog.getEndTime());
                lambdaQueryWrapper.ge(IntTxnLog::getTranDt, intTxnLog.getStartTime());
            }
            if (StringUtils.isNotBlank(intTxnLog.getMerchId())) {
                lambdaQueryWrapper.eq(IntTxnLog::getMerchId, intTxnLog.getMerchId());
            }
            if (StringUtils.isNotBlank(intTxnLog.getOpeCd())) {
                lambdaQueryWrapper.eq(IntTxnLog::getOpeCd, intTxnLog.getOpeCd());
            }
            if (StringUtils.isNotBlank(intTxnLog.getPayId())) {
                lambdaQueryWrapper.eq(IntTxnLog::getPayId, intTxnLog.getPayId());
            }
            if (StringUtils.isNotBlank(intTxnLog.getUserCd()) &&
                    StringUtils.isNotBlank(intTxnLog.getSubOpeCd())) {
                lambdaQueryWrapper.eq(IntTxnLog::getPayId, intTxnLog.getSubOpeCd() + intTxnLog.getUserCd());
            }
            if (StringUtils.isNotBlank(intTxnLog.getAccCardId())){
                lambdaQueryWrapper.eq(IntTxnLog::getAccCardId, intTxnLog.getAccCardId());
            }
        }
        queryWrapper.orderByDesc("tran_dt");
        return queryWrapper;
    }

    @Override
    @DS("datasource2")
    public List<Object> queryTxnLogDetail(IntTxnLogVo intTxnLog) {
        // 不使用分页，直接返回所有数据
        return intTxnLogMapper.queryTxnLogDetail(intTxnLog);
    }

    @Override
    @DS("datasource2")
    public Workbook exportTxnLogDetail(IntTxnLogVo intTxnLog) {
        log.info("获取导出数据>>>>>>>>");
        List<Object> rawData = intTxnLogMapper.queryTxnLogDetail(intTxnLog);

        // 处理数据，应用与前端相同的逻辑
        List<Map<String, Object>> processedData = processExportData(rawData);

        log.info("开始导出缴费明细数据，共{}条记录", processedData.size());

        // 使用EasyPOI模板导出
        return exportWithTemplate(processedData);
    }

    /**
     * 处理导出数据，应用与前端相同的业务逻辑
     */
    private List<Map<String, Object>> processExportData(List<Object> rawData) {
        List<Map<String, Object>> processedList = new ArrayList<>();

        for (Object item : rawData) {
            if (item instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = (Map<String, Object>) item;
                Map<String, Object> processedItem = new HashMap<>(dataMap);

                // 确保str30字段存在
                if (!processedItem.containsKey("str30") || processedItem.get("str30") == null) {
                    processedItem.put("str30", "");
                }

                // 初始化应收字段
                processedItem.put("receivableNum", 0);
                processedItem.put("receivableAmt", 0);

                // 当业务代码为298020时，将num和amt的值赋值给receivableNum和receivableAmt
                String opecd = (String) processedItem.get("opecd");
                if ("298020".equals(opecd)) {
                    processedItem.put("receivableNum", processedItem.get("num"));
                    processedItem.put("receivableAmt", processedItem.get("amt"));
                    processedItem.put("num", 0);
                    processedItem.put("amt", 0);
                }

                // 计算轧差金额
                Object receivableAmtObj = processedItem.get("receivableAmt");
                Object amtObj = processedItem.get("amt");

                double receivableAmt = receivableAmtObj != null ?
                    Double.parseDouble(receivableAmtObj.toString()) / 100 : 0.0;
                double payableAmt = amtObj != null ?
                    Double.parseDouble(amtObj.toString()) / 100 : 0.0;
                double netAmt = receivableAmt - payableAmt;

                processedItem.put("netAmt", String.format("%.2f", netAmt));

                processedList.add(processedItem);
            }
        }

        return processedList;
    }

    /**
     * 使用EasyPOI模板导出Excel
     */
    private Workbook exportWithTemplate(List<Map<String, Object>> data) {
        try {
            TemplateExportParams params = new TemplateExportParams("doc/xzp/缴费明细导出模板.xls");
            Map<String, Object> map = new HashMap<>();
            map.put("txnLogDetailList", data);

            log.info("使用模板导出缴费明细数据，共{}条记录", data.size());
            return ExcelExportUtil.exportExcel(params, map);
        } catch (Exception e) {
            log.error("使用模板导出失败，回退到直接创建Excel方式", e);
            // 如果模板导出失败，回退到直接创建Excel的方式
            return createExcelDirectly(data);
        }
    }

    /**
     * 直接创建Excel文件（备用方法）
     */
    private Workbook createExcelDirectly(List<Map<String, Object>> data) {
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet("缴费明细");

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = {
            "日期", "业务代码_商户号", "子业务代码", "业务名称",
            "商户号", "业务代码", "应付笔数", "应付金额(元)",
            "应收笔数", "应收金额(元)", "轧差金额(元)"
        };

        // 设置表头样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 填充数据
        int rowNum = 1;
        for (Map<String, Object> item : data) {
            Row row = sheet.createRow(rowNum++);

            // 日期
            row.createCell(0).setCellValue(getString(item, "setdate"));
            // 业务代码_商户号
            row.createCell(1).setCellValue(getString(item, "str"));
            // 子业务代码
            row.createCell(2).setCellValue(getString(item, "str30"));
            // 业务名称
            row.createCell(3).setCellValue(getString(item, "str31"));
            // 商户号
            row.createCell(4).setCellValue(getString(item, "merchid"));
            // 业务代码
            row.createCell(5).setCellValue(getString(item, "opecd"));
            // 应付笔数
            row.createCell(6).setCellValue(getNumber(item, "num"));
            // 应付金额(元)
            row.createCell(7).setCellValue(getAmount(item, "amt"));
            // 应收笔数
            row.createCell(8).setCellValue(getNumber(item, "receivableNum"));
            // 应收金额(元)
            row.createCell(9).setCellValue(getAmount(item, "receivableAmt"));
            // 轧差金额(元)
            row.createCell(10).setCellValue(getString(item, "netAmt"));
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        return workbook;
    }

    private String getString(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : "";
    }

    private double getNumber(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return 0;
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    private double getAmount(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return 0;
        try {
            return Double.parseDouble(value.toString()) / 100.0;
        } catch (NumberFormatException e) {
            return 0;
        }
    }
}